import { Router } from 'express';
import { 
  getAccountInfo, 
  getSlot, 
  getBlockHeight,
  subscribeToAccount,
  subscribeToSlot,
  subscribeToLogs
} from '../controllers/solanaController.js';

const router = Router();

// REST endpoints
router.get('/account/:address', getAccountInfo);
router.get('/slot', getSlot);
router.get('/block-height', getBlockHeight);

// Streaming endpoints (these will be handled via WebSocket)
router.post('/subscribe/account', subscribeToAccount);
router.post('/subscribe/slot', subscribeToSlot);
router.post('/subscribe/logs', subscribeToLogs);

export default router;
