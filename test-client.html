<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Solana RPC Streaming Test Client</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 10px;
        }
        .connected { background-color: #d4edda; color: #155724; }
        .disconnected { background-color: #f8d7da; color: #721c24; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        button:disabled { background-color: #6c757d; cursor: not-allowed; }
        input, select {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 5px;
        }
        #messages {
            height: 400px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            background-color: #f8f9fa;
            font-family: monospace;
            font-size: 12px;
        }
        .message {
            margin-bottom: 5px;
            padding: 5px;
            border-left: 3px solid #007bff;
            background-color: white;
        }
        .error { border-left-color: #dc3545; }
        .success { border-left-color: #28a745; }
    </style>
</head>
<body>
    <h1>Solana RPC Streaming Test Client</h1>
    
    <div class="container">
        <h2>Connection Status</h2>
        <div id="status" class="status disconnected">Disconnected</div>
        <button id="connectBtn" onclick="connect()">Connect</button>
        <button id="disconnectBtn" onclick="disconnect()" disabled>Disconnect</button>
    </div>

    <div class="container">
        <h2>Subscriptions</h2>
        
        <div>
            <h3>Slot Updates</h3>
            <button id="slotBtn" onclick="subscribeToSlot()" disabled>Subscribe to Slot Updates</button>
            <button onclick="unsubscribeFromSlot()" disabled id="unsubSlotBtn">Unsubscribe</button>
        </div>

        <div>
            <h3>Account Updates</h3>
            <input type="text" id="accountAddress" placeholder="Account Address" value="11111111111111111111111111111112">
            <button id="accountBtn" onclick="subscribeToAccount()" disabled>Subscribe to Account</button>
            <button onclick="unsubscribeFromAccount()" disabled id="unsubAccountBtn">Unsubscribe</button>
        </div>

        <div>
            <h3>Transaction Logs</h3>
            <select id="logFilter">
                <option value="all">All Transactions</option>
                <option value="mentions">Specific Program</option>
            </select>
            <input type="text" id="programId" placeholder="Program ID (if mentions selected)" style="display:none;">
            <button id="logsBtn" onclick="subscribeToLogs()" disabled>Subscribe to Logs</button>
            <button onclick="unsubscribeFromLogs()" disabled id="unsubLogsBtn">Unsubscribe</button>
        </div>
    </div>

    <div class="container">
        <h2>Messages</h2>
        <button onclick="clearMessages()">Clear Messages</button>
        <div id="messages"></div>
    </div>

    <script>
        let ws = null;
        let subscriptions = {
            slot: null,
            account: null,
            logs: null
        };

        function connect() {
            ws = new WebSocket('ws://localhost:3000');
            
            ws.onopen = function() {
                updateStatus('Connected', true);
                addMessage('Connected to Solana RPC streaming server', 'success');
                enableButtons(true);
            };

            ws.onmessage = function(event) {
                const data = JSON.parse(event.data);
                addMessage(`Received: ${JSON.stringify(data, null, 2)}`, 
                          data.type === 'error' ? 'error' : 'success');
                
                if (data.type === 'subscription_confirmed') {
                    if (data.subscription.startsWith('slot')) {
                        subscriptions.slot = data.subscription;
                        document.getElementById('unsubSlotBtn').disabled = false;
                    } else if (data.subscription.startsWith('account')) {
                        subscriptions.account = data.subscription;
                        document.getElementById('unsubAccountBtn').disabled = false;
                    } else if (data.subscription.startsWith('logs')) {
                        subscriptions.logs = data.subscription;
                        document.getElementById('unsubLogsBtn').disabled = false;
                    }
                }
            };

            ws.onclose = function() {
                updateStatus('Disconnected', false);
                addMessage('Disconnected from server', 'error');
                enableButtons(false);
                resetSubscriptions();
            };

            ws.onerror = function(error) {
                addMessage(`WebSocket error: ${error}`, 'error');
            };
        }

        function disconnect() {
            if (ws) {
                ws.close();
            }
        }

        function subscribeToSlot() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                const message = {
                    type: 'subscribe',
                    subscription: 'slot'
                };
                ws.send(JSON.stringify(message));
                addMessage(`Sent: ${JSON.stringify(message)}`, 'success');
            }
        }

        function subscribeToAccount() {
            const address = document.getElementById('accountAddress').value;
            if (!address) {
                addMessage('Please enter an account address', 'error');
                return;
            }

            if (ws && ws.readyState === WebSocket.OPEN) {
                const message = {
                    type: 'subscribe',
                    subscription: `account_${address}`,
                    params: { address }
                };
                ws.send(JSON.stringify(message));
                addMessage(`Sent: ${JSON.stringify(message)}`, 'success');
            }
        }

        function subscribeToLogs() {
            const filter = document.getElementById('logFilter').value;
            const programId = document.getElementById('programId').value;
            
            if (ws && ws.readyState === WebSocket.OPEN) {
                const message = {
                    type: 'subscribe',
                    subscription: `logs_${filter}`,
                    params: { filter: filter === 'mentions' ? programId : filter }
                };
                ws.send(JSON.stringify(message));
                addMessage(`Sent: ${JSON.stringify(message)}`, 'success');
            }
        }

        function unsubscribeFromSlot() {
            if (subscriptions.slot && ws && ws.readyState === WebSocket.OPEN) {
                const message = {
                    type: 'unsubscribe',
                    subscription: subscriptions.slot
                };
                ws.send(JSON.stringify(message));
                subscriptions.slot = null;
                document.getElementById('unsubSlotBtn').disabled = true;
            }
        }

        function unsubscribeFromAccount() {
            if (subscriptions.account && ws && ws.readyState === WebSocket.OPEN) {
                const message = {
                    type: 'unsubscribe',
                    subscription: subscriptions.account
                };
                ws.send(JSON.stringify(message));
                subscriptions.account = null;
                document.getElementById('unsubAccountBtn').disabled = true;
            }
        }

        function unsubscribeFromLogs() {
            if (subscriptions.logs && ws && ws.readyState === WebSocket.OPEN) {
                const message = {
                    type: 'unsubscribe',
                    subscription: subscriptions.logs
                };
                ws.send(JSON.stringify(message));
                subscriptions.logs = null;
                document.getElementById('unsubLogsBtn').disabled = true;
            }
        }

        function updateStatus(status, connected) {
            const statusEl = document.getElementById('status');
            statusEl.textContent = status;
            statusEl.className = `status ${connected ? 'connected' : 'disconnected'}`;
            
            document.getElementById('connectBtn').disabled = connected;
            document.getElementById('disconnectBtn').disabled = !connected;
        }

        function enableButtons(enabled) {
            document.getElementById('slotBtn').disabled = !enabled;
            document.getElementById('accountBtn').disabled = !enabled;
            document.getElementById('logsBtn').disabled = !enabled;
        }

        function resetSubscriptions() {
            subscriptions = { slot: null, account: null, logs: null };
            document.getElementById('unsubSlotBtn').disabled = true;
            document.getElementById('unsubAccountBtn').disabled = true;
            document.getElementById('unsubLogsBtn').disabled = true;
        }

        function addMessage(message, type = '') {
            const messagesEl = document.getElementById('messages');
            const messageEl = document.createElement('div');
            messageEl.className = `message ${type}`;
            messageEl.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong><br>${message}`;
            messagesEl.appendChild(messageEl);
            messagesEl.scrollTop = messagesEl.scrollHeight;
        }

        function clearMessages() {
            document.getElementById('messages').innerHTML = '';
        }

        // Show/hide program ID input based on filter selection
        document.getElementById('logFilter').addEventListener('change', function() {
            const programIdInput = document.getElementById('programId');
            programIdInput.style.display = this.value === 'mentions' ? 'inline' : 'none';
        });
    </script>
</body>
</html>
