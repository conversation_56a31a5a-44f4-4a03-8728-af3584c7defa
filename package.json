{"name": "bloc<PERSON><PERSON>", "version": "1.0.0", "description": "", "main": "index.js", "type": "module", "scripts": {"dev": "bun --watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@solana/web3.js": "^1.98.4", "@types/ws": "^8.18.1", "cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^5.1.0", "ws": "^8.18.3", "zod": "^4.0.16"}, "devDependencies": {"@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/node": "^24.2.1", "@typescript-eslint/eslint-plugin": "^8.39.0", "@typescript-eslint/parser": "^8.39.0", "dotenv-cli": "^10.0.0", "eslint": "^9.33.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.4", "prettier": "^3.6.2", "ts-node-dev": "^2.0.0", "typescript": "^5.9.2"}}