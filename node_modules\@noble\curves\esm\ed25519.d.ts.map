{"version": 3, "file": "ed25519.d.ts", "sourceRoot": "", "sources": ["../src/ed25519.ts"], "names": [], "mappings": "AAUA,OAAO,EAAa,KAAK,WAAW,EAAE,MAAM,qBAAqB,CAAC;AAClE,OAAO,EACL,iBAAiB,EAEjB,KAAK,OAAO,EAEZ,KAAK,YAAY,EAClB,MAAM,uBAAuB,CAAC;AAC/B,OAAO,EAIL,KAAK,SAAS,EACd,KAAK,aAAa,EAClB,KAAK,SAAS,EACd,KAAK,YAAY,EAClB,MAAM,6BAA6B,CAAC;AACrC,OAAO,EAOL,KAAK,MAAM,EACZ,MAAM,uBAAuB,CAAC;AAC/B,OAAO,EAAc,KAAK,cAAc,IAAI,QAAQ,EAAE,MAAM,0BAA0B,CAAC;AACvF,OAAO,EAA4C,KAAK,GAAG,EAAE,MAAM,YAAY,CAAC;AA+FhF;;;;;;;;;GASG;AACH,eAAO,MAAM,OAAO,EAAE,OAAmE,CAAC;AAY1F,8DAA8D;AAC9D,eAAO,MAAM,UAAU,EAAE,OAIlB,CAAC;AAER,4FAA4F;AAC5F,eAAO,MAAM,SAAS,EAAE,OAMlB,CAAC;AAEP;;;;;;;;;GASG;AACH,eAAO,MAAM,MAAM,EAAE,QAYjB,CAAC;AA0EL,2DAA2D;AAC3D,eAAO,MAAM,cAAc,EAAE,SAAS,CAAC,MAAM,CAavC,CAAC;AA6BP,KAAK,aAAa,GAAG,YAAY,CAAC;AAsClC;;;;;;;;GAQG;AACH,cAAM,eAAgB,SAAQ,iBAAiB,CAAC,eAAe,CAAC;IAI9D,MAAM,CAAC,IAAI,EAAE,eAAe,CACwC;IAEpE,MAAM,CAAC,IAAI,EAAE,eAAe,CACwC;IAEpE,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,MAAM,CAAC,CACM;IAE/B,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,MAAM,CAAC,CACM;gBAEnB,EAAE,EAAE,aAAa;IAI7B,MAAM,CAAC,UAAU,CAAC,EAAE,EAAE,WAAW,CAAC,MAAM,CAAC,GAAG,eAAe;IAI3D,SAAS,CAAC,UAAU,CAAC,KAAK,EAAE,eAAe,GAAG,IAAI;IAIlD,SAAS,CAAC,IAAI,CAAC,EAAE,EAAE,YAAY,GAAG,eAAe;IAIjD,wFAAwF;IACxF,MAAM,CAAC,WAAW,CAAC,GAAG,EAAE,GAAG,GAAG,eAAe;IAI7C,MAAM,CAAC,SAAS,CAAC,KAAK,EAAE,UAAU,GAAG,eAAe;IA4BpD;;;;OAIG;IACH,MAAM,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,GAAG,eAAe;IAIzC,MAAM,CAAC,GAAG,CAAC,MAAM,EAAE,eAAe,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG,eAAe;IAIzE;;;OAGG;IACH,OAAO,IAAI,UAAU;IA4BrB;;;OAGG;IACH,MAAM,CAAC,KAAK,EAAE,eAAe,GAAG,OAAO;IAWvC,GAAG,IAAI,OAAO;CAGf;AAED,eAAO,MAAM,YAAY,EAAE;IACzB,KAAK,EAAE,OAAO,eAAe,CAAC;CACF,CAAC;AAE/B,gEAAgE;AAChE,eAAO,MAAM,mBAAmB,EAAE,aAAa,CAAC,MAAM,CAUrD,CAAC;AAUF;;;;;GAKG;AACH,eAAO,MAAM,wBAAwB,EAAE,MAAM,EAS5C,CAAC;AAEF,mDAAmD;AACnD,wBAAgB,sBAAsB,CAAC,UAAU,EAAE,GAAG,GAAG,UAAU,CAElE;AACD,mDAAmD;AACnD,eAAO,MAAM,mBAAmB,EAAE,OAAO,sBAA+C,CAAC;AAEzF,uDAAuD;AACvD,wBAAgB,uBAAuB,CAAC,WAAW,EAAE,UAAU,GAAG,UAAU,CAE3E;AAED,2CAA2C;AAC3C,eAAO,MAAM,cAAc,EAAE,OAAO,eAAiC,CAAC;AACtE,mFAAmF;AACnF,eAAO,MAAM,WAAW,EAAE,SAAS,CAAC,MAAM,CAAwD,CAAC;AACnG,mFAAmF;AACnF,eAAO,MAAM,aAAa,EAAE,SAAS,CAAC,MAAM,CACX,CAAC;AAClC,KAAK,UAAU,GAAG,CAAC,GAAG,EAAE,UAAU,EAAE,OAAO,EAAE,YAAY,KAAK,eAAe,CAAC;AAC9E,wFAAwF;AACxF,eAAO,MAAM,kBAAkB,EAAE,UACiB,CAAC;AACnD,wFAAwF;AACxF,eAAO,MAAM,oBAAoB,EAAE,UACe,CAAC"}