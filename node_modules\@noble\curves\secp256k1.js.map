{"version": 3, "file": "secp256k1.js", "sourceRoot": "", "sources": ["src/secp256k1.ts"], "names": [], "mappings": ";;;AAAA;;;;;;GAMG;AACH,sEAAsE;AACtE,mDAA+C;AAC/C,qDAAqD;AACrD,yDAAyE;AAEzE,kEAKqC;AACrC,sDAAyE;AACzE,8DAOmC;AAEnC,yCAOoB;AAEpB,oDAAoD;AACpD,0DAA0D;AAC1D,iEAAiE;AACjE,MAAM,eAAe,GAA4B;IAC/C,CAAC,EAAE,MAAM,CAAC,oEAAoE,CAAC;IAC/E,CAAC,EAAE,MAAM,CAAC,oEAAoE,CAAC;IAC/E,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;IACZ,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;IACZ,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;IACZ,EAAE,EAAE,MAAM,CAAC,oEAAoE,CAAC;IAChF,EAAE,EAAE,MAAM,CAAC,oEAAoE,CAAC;CACjF,CAAC;AAEF,MAAM,cAAc,GAAqB;IACvC,IAAI,EAAE,MAAM,CAAC,oEAAoE,CAAC;IAClF,OAAO,EAAE;QACP,CAAC,MAAM,CAAC,oCAAoC,CAAC,EAAE,CAAC,MAAM,CAAC,oCAAoC,CAAC,CAAC;QAC7F,CAAC,MAAM,CAAC,qCAAqC,CAAC,EAAE,MAAM,CAAC,oCAAoC,CAAC,CAAC;KAC9F;CACF,CAAC;AAEF,MAAM,GAAG,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AACtC,MAAM,GAAG,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AACtC,MAAM,GAAG,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AAEtC;;;GAGG;AACH,SAAS,OAAO,CAAC,CAAS;IACxB,MAAM,CAAC,GAAG,eAAe,CAAC,CAAC,CAAC;IAC5B,kBAAkB;IAClB,MAAM,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,GAAG,MAAM,CAAC,EAAE,CAAC,EAAE,IAAI,GAAG,MAAM,CAAC,EAAE,CAAC,CAAC;IAC7E,kBAAkB;IAClB,MAAM,IAAI,GAAG,MAAM,CAAC,EAAE,CAAC,EAAE,IAAI,GAAG,MAAM,CAAC,EAAE,CAAC,EAAE,IAAI,GAAG,MAAM,CAAC,EAAE,CAAC,CAAC;IAC9D,MAAM,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,UAAU;IACtC,MAAM,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM;IACpC,MAAM,EAAE,GAAG,CAAC,IAAA,iBAAI,EAAC,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC;IACvC,MAAM,EAAE,GAAG,CAAC,IAAA,iBAAI,EAAC,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC;IACvC,MAAM,GAAG,GAAG,CAAC,IAAA,iBAAI,EAAC,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC;IACxC,MAAM,GAAG,GAAG,CAAC,IAAA,iBAAI,EAAC,GAAG,EAAE,IAAI,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;IAC3C,MAAM,GAAG,GAAG,CAAC,IAAA,iBAAI,EAAC,GAAG,EAAE,IAAI,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;IAC3C,MAAM,GAAG,GAAG,CAAC,IAAA,iBAAI,EAAC,GAAG,EAAE,IAAI,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;IAC3C,MAAM,IAAI,GAAG,CAAC,IAAA,iBAAI,EAAC,GAAG,EAAE,IAAI,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;IAC5C,MAAM,IAAI,GAAG,CAAC,IAAA,iBAAI,EAAC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;IAC7C,MAAM,IAAI,GAAG,CAAC,IAAA,iBAAI,EAAC,IAAI,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC;IAC3C,MAAM,EAAE,GAAG,CAAC,IAAA,iBAAI,EAAC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;IAC3C,MAAM,EAAE,GAAG,CAAC,IAAA,iBAAI,EAAC,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC;IACvC,MAAM,IAAI,GAAG,IAAA,iBAAI,EAAC,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;IAC9B,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAAE,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;IAC7E,OAAO,IAAI,CAAC;AACd,CAAC;AAED,MAAM,IAAI,GAAG,IAAA,kBAAK,EAAC,eAAe,CAAC,CAAC,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,CAAC;AAEzD;;;;;;;;;;;;;GAaG;AACU,QAAA,SAAS,GAAsB,IAAA,8BAAW,EACrD,EAAE,GAAG,eAAe,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,cAAc,EAAE,EAClE,gBAAM,CACP,CAAC;AAEF,+FAA+F;AAC/F,iEAAiE;AACjE,wFAAwF;AACxF,MAAM,oBAAoB,GAAkC,EAAE,CAAC;AAC/D,SAAS,UAAU,CAAC,GAAW,EAAE,GAAG,QAAsB;IACxD,IAAI,IAAI,GAAG,oBAAoB,CAAC,GAAG,CAAC,CAAC;IACrC,IAAI,IAAI,KAAK,SAAS,EAAE,CAAC;QACvB,MAAM,IAAI,GAAG,IAAA,gBAAM,EAAC,IAAA,sBAAW,EAAC,GAAG,CAAC,CAAC,CAAC;QACtC,IAAI,GAAG,IAAA,sBAAW,EAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QAC/B,oBAAoB,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;IACnC,CAAC;IACD,OAAO,IAAA,gBAAM,EAAC,IAAA,sBAAW,EAAC,IAAI,EAAE,GAAG,QAAQ,CAAC,CAAC,CAAC;AAChD,CAAC;AAED,oFAAoF;AACpF,MAAM,YAAY,GAAG,CAAC,KAAwB,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAChF,MAAM,OAAO,GAAG,eAAe,CAAC,CAAC,GAAG,EAAE,CAAC,iBAAS,CAAC,KAAK,CAAC,EAAE,CAAC;AAC1D,MAAM,OAAO,GAAG,CAAC,CAAS,EAAE,EAAE,CAAC,CAAC,GAAG,GAAG,KAAK,GAAG,CAAC;AAE/C,oCAAoC;AACpC,SAAS,mBAAmB,CAAC,IAAa;IACxC,MAAM,EAAE,EAAE,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;IAC7B,MAAM,EAAE,GAAG,IAAA,+BAAc,EAAC,EAAE,EAAE,IAAI,CAAC,CAAC;IACpC,MAAM,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,4CAA4C;IACzE,MAAM,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;IAC9C,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC;AAC5C,CAAC;AACD;;;GAGG;AACH,SAAS,MAAM,CAAC,CAAS;IACvB,MAAM,EAAE,GAAG,IAAI,CAAC;IAChB,IAAI,CAAC,EAAE,CAAC,WAAW,CAAC,CAAC,CAAC;QAAE,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;IACpE,MAAM,EAAE,GAAG,EAAE,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IAC5B,MAAM,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,EAAE,GAAG,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,wBAAwB;IACjE,IAAI,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,2CAA2C;IAC/D,mDAAmD;IACnD,mDAAmD;IACnD,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;QAAE,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAC/B,MAAM,CAAC,GAAG,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACvC,CAAC,CAAC,cAAc,EAAE,CAAC;IACnB,OAAO,CAAC,CAAC;AACX,CAAC;AACD,MAAM,GAAG,GAAG,0BAAe,CAAC;AAC5B;;GAEG;AACH,SAAS,SAAS,CAAC,GAAG,IAAkB;IACtC,OAAO,OAAO,CAAC,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,UAAU,CAAC,mBAAmB,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;AAC1E,CAAC;AAED;;GAEG;AACH,SAAS,mBAAmB,CAAC,SAAc;IACzC,OAAO,mBAAmB,CAAC,SAAS,CAAC,CAAC,KAAK,CAAC,CAAC,oDAAoD;AACnG,CAAC;AAED;;;GAGG;AACH,SAAS,WAAW,CAAC,OAAY,EAAE,SAAkB,EAAE,UAAe,IAAA,sBAAW,EAAC,EAAE,CAAC;IACnF,MAAM,EAAE,EAAE,EAAE,GAAG,OAAO,CAAC;IACvB,MAAM,CAAC,GAAG,IAAA,sBAAW,EAAC,SAAS,EAAE,OAAO,CAAC,CAAC;IAC1C,MAAM,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,GAAG,mBAAmB,CAAC,SAAS,CAAC,CAAC,CAAC,gCAAgC;IACjG,MAAM,CAAC,GAAG,IAAA,sBAAW,EAAC,SAAS,EAAE,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC,2CAA2C;IAC1F,MAAM,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,CAAC,GAAG,GAAG,CAAC,UAAU,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,yDAAyD;IACtH,MAAM,IAAI,GAAG,UAAU,CAAC,eAAe,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,4CAA4C;IAChG,yDAAyD;IACzD,MAAM,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,GAAG,mBAAmB,CAAC,IAAI,CAAC,CAAC;IAC3D,MAAM,CAAC,GAAG,SAAS,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,gEAAgE;IAChG,MAAM,GAAG,GAAG,IAAI,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC,+CAA+C;IAC/E,GAAG,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;IACf,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;IAC9C,iEAAiE;IACjE,IAAI,CAAC,aAAa,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC;QAAE,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;IACpF,OAAO,GAAG,CAAC;AACb,CAAC;AAED;;;GAGG;AACH,SAAS,aAAa,CAAC,SAAc,EAAE,OAAY,EAAE,SAAc;IACjE,MAAM,EAAE,EAAE,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;IAC7B,MAAM,GAAG,GAAG,IAAA,sBAAW,EAAC,WAAW,EAAE,SAAS,EAAE,EAAE,CAAC,CAAC;IACpD,MAAM,CAAC,GAAG,IAAA,sBAAW,EAAC,SAAS,EAAE,OAAO,CAAC,CAAC;IAC1C,MAAM,GAAG,GAAG,IAAA,sBAAW,EAAC,WAAW,EAAE,SAAS,EAAE,EAAE,CAAC,CAAC;IACpD,IAAI,CAAC;QACH,MAAM,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,0CAA0C;QACtE,MAAM,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,yCAAyC;QAC7E,IAAI,CAAC,IAAA,kBAAO,EAAC,CAAC,EAAE,GAAG,EAAE,eAAe,CAAC,CAAC,CAAC;YAAE,OAAO,KAAK,CAAC;QACtD,MAAM,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,0CAA0C;QAC/E,IAAI,CAAC,IAAA,kBAAO,EAAC,CAAC,EAAE,GAAG,EAAE,eAAe,CAAC,CAAC,CAAC;YAAE,OAAO,KAAK,CAAC;QACtD,0CAA0C;QAC1C,MAAM,CAAC,GAAG,SAAS,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACvD,qCAAqC;QACrC,MAAM,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,cAAc,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClE,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAC;QAC9B,yDAAyD;QACzD,IAAI,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC;YAAE,OAAO,KAAK,CAAC;QACpD,OAAO,IAAI,CAAC;IACd,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,KAAK,CAAC;IACf,CAAC;AACH,CAAC;AAyBD;;;;;;;;;;;;GAYG;AACU,QAAA,OAAO,GAAgC,CAAC,GAAG,EAAE;IACxD,MAAM,IAAI,GAAG,EAAE,CAAC;IAChB,MAAM,UAAU,GAAG,EAAE,CAAC;IACtB,MAAM,eAAe,GAAG,CAAC,IAAI,GAAG,IAAA,sBAAW,EAAC,UAAU,CAAC,EAAc,EAAE;QACrE,OAAO,IAAA,2BAAc,EAAC,IAAI,EAAE,eAAe,CAAC,CAAC,CAAC,CAAC;IACjD,CAAC,CAAC;IACF,eAAe;IACf,iBAAS,CAAC,KAAK,CAAC,eAAe,CAAC;IAChC,SAAS,MAAM,CAAC,IAAiB;QAC/B,MAAM,SAAS,GAAG,eAAe,CAAC,IAAI,CAAC,CAAC;QACxC,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,mBAAmB,CAAC,SAAS,CAAC,EAAE,CAAC;IAClE,CAAC;IACD,OAAO;QACL,MAAM;QACN,YAAY,EAAE,mBAAmB;QACjC,IAAI,EAAE,WAAW;QACjB,MAAM,EAAE,aAAa;QACrB,KAAK,EAAE,OAAO;QACd,KAAK,EAAE;YACL,eAAe,EAAE,eAAe;YAChC,gBAAgB,EAAE,eAAe;YACjC,UAAU;YAEV,eAAe;YACf,MAAM;YACN,YAAY;YACZ,eAAe,EAAf,0BAAe;YACf,eAAe,EAAf,0BAAe;YACf,GAAG,EAAH,gBAAG;SACJ;QACD,OAAO,EAAE;YACP,SAAS,EAAE,IAAI;YACf,SAAS,EAAE,IAAI;YACf,kBAAkB,EAAE,KAAK;YACzB,SAAS,EAAE,IAAI,GAAG,CAAC;YACnB,IAAI,EAAE,UAAU;SACjB;KACF,CAAC;AACJ,CAAC,CAAC,EAAE,CAAC;AAEL,MAAM,MAAM,GAAG,eAAe,CAAC,CAAC,GAAG,EAAE,CACnC,IAAA,6BAAU,EACR,IAAI,EACJ;IACE,OAAO;IACP;QACE,oEAAoE;QACpE,mEAAmE;QACnE,oEAAoE;QACpE,oEAAoE;KACrE;IACD,OAAO;IACP;QACE,oEAAoE;QACpE,oEAAoE;QACpE,oEAAoE,EAAE,SAAS;KAChF;IACD,OAAO;IACP;QACE,oEAAoE;QACpE,oEAAoE;QACpE,oEAAoE;QACpE,oEAAoE;KACrE;IACD,OAAO;IACP;QACE,oEAAoE;QACpE,oEAAoE;QACpE,oEAAoE;QACpE,oEAAoE,EAAE,SAAS;KAChF;CACF,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAA6C,CAClF,CAAC,EAAE,CAAC;AACP,MAAM,MAAM,GAAG,eAAe,CAAC,CAAC,GAAG,EAAE,CACnC,IAAA,oCAAmB,EAAC,IAAI,EAAE;IACxB,CAAC,EAAE,MAAM,CAAC,oEAAoE,CAAC;IAC/E,CAAC,EAAE,MAAM,CAAC,MAAM,CAAC;IACjB,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;CAC9B,CAAC,CAAC,EAAE,CAAC;AAER,wEAAwE;AAC3D,QAAA,gBAAgB,GAAsC,CAAC,GAAG,EAAE,CACvE,IAAA,+BAAY,EACV,iBAAS,CAAC,KAAK,EACf,CAAC,OAAiB,EAAE,EAAE;IACpB,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjD,OAAO,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACtB,CAAC,EACD;IACE,GAAG,EAAE,gCAAgC;IACrC,SAAS,EAAE,gCAAgC;IAC3C,CAAC,EAAE,IAAI,CAAC,KAAK;IACb,CAAC,EAAE,CAAC;IACJ,CAAC,EAAE,GAAG;IACN,MAAM,EAAE,KAAK;IACb,IAAI,EAAE,gBAAM;CACb,CACF,CAAC,EAAE,CAAC;AAEP,uFAAuF;AAC1E,QAAA,WAAW,GAAsC,CAAC,GAAG,EAAE,CAClE,wBAAgB,CAAC,WAAW,CAAC,EAAE,CAAC;AAElC,uFAAuF;AAC1E,QAAA,aAAa,GAAsC,CAAC,GAAG,EAAE,CACpE,wBAAgB,CAAC,aAAa,CAAC,EAAE,CAAC"}