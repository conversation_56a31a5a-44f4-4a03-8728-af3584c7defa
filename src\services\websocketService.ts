import WebSocket from 'ws';
import { 
  handleAccountSubscription, 
  handleSlotSubscription, 
  handleLogsSubscription,
  removeSubscription,
  activeSubscriptions
} from '../controllers/solanaController.js';

export interface SubscriptionMessage {
  type: 'subscribe' | 'unsubscribe';
  subscription: string;
  params?: {
    address?: string;
    filter?: string;
    encoding?: string;
  };
}

export class WebSocketService {
  private clientSubscriptions = new Map<WebSocket, Set<number>>();

  handleConnection(ws: WebSocket): void {
    console.log('New WebSocket connection established');
    this.clientSubscriptions.set(ws, new Set());

    ws.on('message', async (message) => {
      try {
        const data: SubscriptionMessage = JSON.parse(message.toString());
        await this.handleMessage(ws, data);
      } catch (error) {
        console.error('Error parsing WebSocket message:', error);
        this.sendError(ws, 'Invalid message format');
      }
    });

    ws.on('close', () => {
      console.log('WebSocket connection closed');
      this.cleanup(ws);
    });

    ws.on('error', (error) => {
      console.error('WebSocket error:', error);
      this.cleanup(ws);
    });

    // Send welcome message
    this.sendMessage(ws, {
      type: 'connected',
      message: 'Connected to Solana RPC streaming service',
      availableSubscriptions: [
        'account_<address>',
        'slot',
        'logs_<filter>'
      ]
    });
  }

  private async handleMessage(ws: WebSocket, data: SubscriptionMessage): Promise<void> {
    switch (data.type) {
      case 'subscribe':
        await this.handleSubscribe(ws, data);
        break;
      case 'unsubscribe':
        await this.handleUnsubscribe(ws, data);
        break;
      default:
        this.sendError(ws, 'Unknown message type');
    }
  }

  private async handleSubscribe(ws: WebSocket, data: SubscriptionMessage): Promise<void> {
    const { subscription, params = {} } = data;
    
    try {
      let subscriptionId: number | null = null;

      if (subscription.startsWith('account_')) {
        const address = params.address || subscription.split('_')[1];
        if (!address) {
          this.sendError(ws, 'Account address is required for account subscription');
          return;
        }
        subscriptionId = await handleAccountSubscription(ws, address);

      } else if (subscription.startsWith('slot')) {
        subscriptionId = await handleSlotSubscription(ws);

      } else if (subscription.startsWith('logs_')) {
        const filter = params.filter || subscription.split('_')[1] || 'all';
        subscriptionId = await handleLogsSubscription(ws, filter);

      } else {
        this.sendError(ws, 'Unknown subscription type');
        return;
      }

      if (subscriptionId !== null) {
        // Store the subscription for this client
        const clientSubs = this.clientSubscriptions.get(ws);
        if (clientSubs) {
          clientSubs.add(subscriptionId);
        }

        // Store globally
        activeSubscriptions.set(subscription, subscriptionId);

        this.sendMessage(ws, {
          type: 'subscription_confirmed',
          subscription,
          subscriptionId,
          message: `Successfully subscribed to ${subscription}`
        });

        console.log(`Client subscribed to ${subscription} with ID ${subscriptionId}`);
      } else {
        this.sendError(ws, 'Failed to create subscription');
      }
    } catch (error) {
      console.error('Error handling subscription:', error);
      this.sendError(ws, 'Failed to process subscription request');
    }
  }

  private async handleUnsubscribe(ws: WebSocket, data: SubscriptionMessage): Promise<void> {
    const { subscription } = data;
    
    try {
      const subscriptionId = activeSubscriptions.get(subscription);
      
      if (subscriptionId !== undefined) {
        await removeSubscription(subscriptionId);
        
        // Remove from client subscriptions
        const clientSubs = this.clientSubscriptions.get(ws);
        if (clientSubs) {
          clientSubs.delete(subscriptionId);
        }

        // Remove from global subscriptions
        activeSubscriptions.delete(subscription);

        this.sendMessage(ws, {
          type: 'unsubscribed',
          subscription,
          message: `Successfully unsubscribed from ${subscription}`
        });

        console.log(`Client unsubscribed from ${subscription}`);
      } else {
        this.sendError(ws, 'Subscription not found');
      }
    } catch (error) {
      console.error('Error handling unsubscribe:', error);
      this.sendError(ws, 'Failed to process unsubscribe request');
    }
  }

  private async cleanup(ws: WebSocket): Promise<void> {
    const clientSubs = this.clientSubscriptions.get(ws);
    
    if (clientSubs) {
      // Remove all subscriptions for this client
      for (const subscriptionId of clientSubs) {
        try {
          await removeSubscription(subscriptionId);
        } catch (error) {
          console.error('Error removing subscription during cleanup:', error);
        }
      }
      
      // Remove client from tracking
      this.clientSubscriptions.delete(ws);
    }
  }

  private sendMessage(ws: WebSocket, message: any): void {
    if (ws.readyState === WebSocket.OPEN) {
      ws.send(JSON.stringify(message));
    }
  }

  private sendError(ws: WebSocket, error: string): void {
    this.sendMessage(ws, {
      type: 'error',
      message: error,
      timestamp: new Date().toISOString()
    });
  }

  // Get statistics about active connections and subscriptions
  getStats(): any {
    return {
      activeConnections: this.clientSubscriptions.size,
      totalSubscriptions: activeSubscriptions.size,
      subscriptionsByType: this.getSubscriptionsByType()
    };
  }

  private getSubscriptionsByType(): Record<string, number> {
    const stats: Record<string, number> = {};
    
    for (const subscription of activeSubscriptions.keys()) {
      const type = subscription.split('_')[0];
      stats[type] = (stats[type] || 0) + 1;
    }
    
    return stats;
  }
}

export const websocketService = new WebSocketService();
