# Solana RPC Streaming API

A Node.js/Express API that streams real-time data from the Solana blockchain using WebSockets.

## Features

- **Real-time Account Updates**: Subscribe to account changes
- **Slot Updates**: Get notified of new slots
- **Transaction Logs**: Stream transaction logs
- **REST API**: Traditional HTTP endpoints for one-time queries
- **WebSocket Streaming**: Real-time data streaming
- **TypeScript**: Full type safety

## Installation

1. Clone the repository
2. Install dependencies:
   ```bash
   bun install
   ```
3. Copy environment variables:
   ```bash
   cp .env.example .env
   ```
4. Start the development server:
   ```bash
   bun run dev
   ```

## API Endpoints

### REST Endpoints

- `GET /api/health` - Health check
- `GET /api/solana/account/:address` - Get account information
- `GET /api/solana/slot` - Get current slot
- `GET /api/solana/block-height` - Get current block height
- `GET /api/stats` - Get WebSocket connection statistics

### WebSocket Streaming

Connect to WebSocket at `ws://localhost:3000` and send subscription messages:

#### Account Subscription
```json
{
  "type": "subscribe",
  "subscription": "account_<address>",
  "params": {
    "address": "11111111111111111111111111111112"
  }
}
```

#### Slot Subscription
```json
{
  "type": "subscribe",
  "subscription": "slot"
}
```

#### Logs Subscription
```json
{
  "type": "subscribe",
  "subscription": "logs_all",
  "params": {
    "filter": "all"
  }
}
```

#### Unsubscribe
```json
{
  "type": "unsubscribe",
  "subscription": "account_<address>"
}
```

## Example Usage

### Using WebSocket in JavaScript

```javascript
const ws = new WebSocket('ws://localhost:3000');

ws.onopen = () => {
  console.log('Connected to Solana streaming API');
  
  // Subscribe to slot updates
  ws.send(JSON.stringify({
    type: 'subscribe',
    subscription: 'slot'
  }));
};

ws.onmessage = (event) => {
  const data = JSON.parse(event.data);
  console.log('Received:', data);
};
```

### Using curl for REST API

```bash
# Get account info
curl http://localhost:3000/api/solana/account/11111111111111111111111111111112

# Get current slot
curl http://localhost:3000/api/solana/slot

# Get connection stats
curl http://localhost:3000/api/stats
```

## Environment Variables

- `PORT` - Server port (default: 3000)
- `SOLANA_RPC_URL` - Solana RPC endpoint (default: https://solana-rpc.publicnode.com)
- `NODE_ENV` - Environment (development/production)
- `CORS_ORIGIN` - CORS origin (default: *)

## Development

```bash
# Start development server with hot reload
bun run dev

# Build for production
bun run build

# Start production server
bun run start
```

## WebSocket Message Types

### Incoming Messages
- `subscribe` - Subscribe to a data stream
- `unsubscribe` - Unsubscribe from a data stream

### Outgoing Messages
- `connected` - Connection established
- `subscription_confirmed` - Subscription successful
- `account_update` - Account data changed
- `slot_update` - New slot information
- `logs_update` - New transaction logs
- `error` - Error message
- `unsubscribed` - Unsubscription confirmed
