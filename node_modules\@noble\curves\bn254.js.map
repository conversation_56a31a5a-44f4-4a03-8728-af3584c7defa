{"version": 3, "file": "bn254.js", "sourceRoot": "", "sources": ["src/bn254.ts"], "names": [], "mappings": ";;;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAsDG;AACH,sEAAsE;AACtE,mDAA+C;AAC/C,8CAK2B;AAC3B,sDAA2D;AAE3D,kDAA4D;AAC5D,8DAA4F;AAC5F,yCAAoD;AACpD,kBAAkB;AAClB,MAAM,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AACzE,MAAM,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AAEtB,MAAM,IAAI,GAAG,MAAM,CAAC,qBAAqB,CAAC,CAAC;AAC3C,MAAM,QAAQ,GAAG,IAAA,iBAAM,EAAC,IAAI,CAAC,CAAC;AAC9B,MAAM,aAAa,GAAG,GAAG,GAAG,IAAI,IAAI,GAAG,CAAC;AAExC,MAAM,cAAc,GAA4B;IAC9C,CAAC,EAAE,MAAM,CAAC,oEAAoE,CAAC;IAC/E,CAAC,EAAE,MAAM,CAAC,oEAAoE,CAAC;IAC/E,CAAC,EAAE,GAAG;IACN,CAAC,EAAE,GAAG;IACN,CAAC,EAAE,GAAG;IACN,EAAE,EAAE,GAAG;IACP,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC;CACd,CAAC;AAEF,SAAS;AACT,+EAA+E;AAClE,QAAA,QAAQ,GAAmB,IAAA,kBAAK,EAAC,cAAc,CAAC,CAAC,CAAC,CAAC;AAEhE,iDAAiD;AACjD,MAAM,IAAI,GAAG;IACX,EAAE,EAAE,MAAM,CAAC,+EAA+E,CAAC;IAC3F,EAAE,EAAE,MAAM,CAAC,6EAA6E,CAAC;CAC1F,CAAC;AAEF,MAAM,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,IAAA,kBAAO,EAAC;IACrC,KAAK,EAAE,cAAc,CAAC,CAAC;IACvB,KAAK,EAAE,QAAQ;IACf,cAAc,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC;IAChC,SAAS,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC;IACtC,qBAAqB,EAAE,CAAC,GAAG,EAAE,EAAE;QAC7B,MAAM,SAAS,GAAG,CAAC,GAAS,EAAE,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC;QAChF,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;QACxD,MAAM,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QACjD,MAAM,EAAE,GAAG,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;QAChD,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,iBAAiB,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;QACpD,MAAM,EAAE,GAAG,SAAS,CAAC,EAAE,CAAC,CAAC;QACzB,MAAM,EAAE,GAAG,SAAS,CAAC,IAAI,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAC,CAAC;QACjD,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;QAC1E,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;QAC5B,OAAO,IAAI,CAAC,GAAG,CACb,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EACrD,IAAI,CAAC,GAAG,CACN,IAAI,CAAC,YAAY,CAAC,EAAE,EAAE,CAAC,CAAC,EACxB,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAClE,CACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC;AAEH,sBAAsB;AACtB,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,IAAA,uBAAY,EAAC,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC,UAAU,CAAC,CAAC;AAE7D;;;;;;EAME;AACF,MAAM,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC;IAChC,wDAAwD;IACxD,GAAG,EAAE,8BAA8B;IACnC,SAAS,EAAE,8BAA8B;IACzC,CAAC,EAAE,EAAE,CAAC,KAAK;IACX,CAAC,EAAE,CAAC;IACJ,CAAC,EAAE,GAAG;IACN,MAAM,EAAE,KAAK;IACb,IAAI,EAAE,gBAAM;CACb,CAAC,CAAC;AAEI,MAAM,eAAe,GAAqB,CAC/C,EAAO,EACP,EAAO,EACP,EAAO,EACP,EAAO,EACP,EAAO,EACP,QAAkC,EAClC,EAAE;IACF,MAAM,CAAC,GAAG,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;IACtB,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,QAAQ,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpD,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3B,QAAQ,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC9C,CAAC,CAAC;AAZW,QAAA,eAAe,mBAY1B;AAEF,2DAA2D;AAC3D,MAAM,cAAc,GAAyB;IAC3C,CAAC,EAAE,GAAG,CAAC,KAAK;IACZ,CAAC,EAAE,cAAc,CAAC,CAAC;IACnB,CAAC,EAAE,MAAM,CAAC,oEAAoE,CAAC;IAC/E,CAAC,EAAE,GAAG,CAAC,IAAI;IACX,CAAC,EAAE,IAAI;IACP,EAAE,EAAE,GAAG,CAAC,YAAY,CAAC;QACnB,MAAM,CAAC,+EAA+E,CAAC;QACvF,MAAM,CAAC,+EAA+E,CAAC;KACxF,CAAC;IACF,EAAE,EAAE,GAAG,CAAC,YAAY,CAAC;QACnB,MAAM,CAAC,8EAA8E,CAAC;QACtF,MAAM,CAAC,8EAA8E,CAAC;KACvF,CAAC;CACH,CAAC;AAEF;;;GAGG;AACU,QAAA,KAAK,GAAe,IAAA,YAAG,EAAC;IACnC,SAAS;IACT,MAAM,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,EAAE,EAAE,gBAAQ,EAAE;IAC5C,EAAE,EAAE;QACF,GAAG,cAAc;QACjB,EAAE;QACF,WAAW,EAAE,EAAE,GAAG,WAAW,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,8BAA8B,EAAE;QAC1E,cAAc,EAAE,IAAI;QACpB,kBAAkB,EAAE,IAAI;QACxB,UAAU,EAAE,yBAAc;QAC1B,SAAS,EAAE,yBAAc;QACzB,OAAO,EAAE,yBAAc;QACvB,cAAc,EAAE;YACd,SAAS,EAAE,yBAAc;YACzB,OAAO,EAAE,yBAAc;YACvB,OAAO,EAAE,yBAAc;YACvB,UAAU,EAAE,yBAAc;YAC1B,KAAK,EAAE,yBAAc;SACtB;KACF;IACD,EAAE,EAAE;QACF,GAAG,cAAc;QACjB,EAAE,EAAE,GAAG;QACP,IAAI,EAAE,MAAM,CAAC,+EAA+E,CAAC;QAC7F,WAAW,EAAE,EAAE,GAAG,WAAW,EAAE;QAC/B,cAAc,EAAE,IAAI;QACpB,kBAAkB,EAAE,IAAI;QACxB,aAAa,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,iBAAiB;QAC/F,UAAU,EAAE,yBAAc;QAC1B,SAAS,EAAE,yBAAc;QACzB,OAAO,EAAE,yBAAc;QACvB,SAAS,EAAE;YACT,SAAS,EAAE,yBAAc;YACzB,OAAO,EAAE,yBAAc;YACvB,OAAO,EAAE,yBAAc;YACvB,UAAU,EAAE,yBAAc;YAC1B,KAAK,EAAE,yBAAc;SACtB;KACF;IACD,MAAM,EAAE;QACN,WAAW,EAAE,IAAI,GAAG,GAAG,GAAG,GAAG;QAC7B,CAAC,EAAE,gBAAQ,CAAC,KAAK;QACjB,SAAS,EAAE,KAAK;QAChB,SAAS,EAAE,UAAU;KACtB;IACD,WAAW;IACX,IAAI,EAAE,gBAAM;IACZ,cAAc,EAAE,uBAAe;CAChC,CAAC,CAAC;AAEH;;;;;GAKG;AACU,QAAA,iBAAiB,GAAY,IAAA,4BAAW,EAAC;IACpD,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;IACZ,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;IACZ,EAAE;IACF,CAAC,EAAE,MAAM,CAAC,+EAA+E,CAAC;IAC1F,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC;IACb,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC;IACb,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;IACZ,IAAI,EAAE,gBAAM;CACb,CAAC,CAAC"}