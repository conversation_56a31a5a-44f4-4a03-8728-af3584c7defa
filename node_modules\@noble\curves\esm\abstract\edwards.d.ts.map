{"version": 3, "file": "edwards.d.ts", "sourceRoot": "", "sources": ["../../src/abstract/edwards.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AACH,sEAAsE;AACtE,OAAO,EAcL,KAAK,KAAK,EACV,KAAK,GAAG,EACT,MAAM,aAAa,CAAC;AACrB,OAAO,EAKL,KAAK,WAAW,EAChB,KAAK,UAAU,EACf,KAAK,YAAY,EACjB,KAAK,UAAU,EACf,KAAK,cAAc,EACpB,MAAM,YAAY,CAAC;AACpB,OAAO,EAAS,KAAK,MAAM,EAAE,KAAK,OAAO,EAAE,MAAM,cAAc,CAAC;AAMhE,MAAM,MAAM,OAAO,GAAG,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,KAAK;IAAE,OAAO,EAAE,OAAO,CAAC;IAAC,KAAK,EAAE,MAAM,CAAA;CAAE,CAAC;AAEpF,iEAAiE;AACjE,MAAM,WAAW,YAAa,SAAQ,UAAU,CAAC,MAAM,EAAE,YAAY,CAAC;IACpE,sDAAsD;IACtD,QAAQ,CAAC,CAAC,EAAE,MAAM,CAAC;IACnB,sDAAsD;IACtD,QAAQ,CAAC,CAAC,EAAE,MAAM,CAAC;IACnB,4BAA4B;IAC5B,QAAQ,CAAC,CAAC,EAAE,MAAM,CAAC;IACnB,4BAA4B;IAC5B,QAAQ,CAAC,CAAC,EAAE,MAAM,CAAC;IAEnB,gCAAgC;IAChC,UAAU,IAAI,UAAU,CAAC;IACzB,iDAAiD;IACjD,cAAc,CAAC,UAAU,EAAE,MAAM,GAAG,IAAI,CAAC;IACzC,yBAAyB;IACzB,QAAQ,CAAC,EAAE,EAAE,MAAM,CAAC;IACpB,yBAAyB;IACzB,QAAQ,CAAC,EAAE,EAAE,MAAM,CAAC;IACpB,yBAAyB;IACzB,QAAQ,CAAC,EAAE,EAAE,MAAM,CAAC;IACpB,yBAAyB;IACzB,QAAQ,CAAC,EAAE,EAAE,MAAM,CAAC;CACrB;AACD,uEAAuE;AACvE,MAAM,WAAW,gBAAiB,SAAQ,cAAc,CAAC,YAAY,CAAC;IACpE,KAAK,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,GAAG,YAAY,CAAC;IAC/D,KAAK,IAAI,WAAW,CAAC;IACrB,SAAS,CAAC,KAAK,EAAE,UAAU,EAAE,MAAM,CAAC,EAAE,OAAO,GAAG,YAAY,CAAC;IAC7D,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,MAAM,CAAC,EAAE,OAAO,GAAG,YAAY,CAAC;IAClD,qFAAqF;IACrF,GAAG,CAAC,MAAM,EAAE,YAAY,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG,YAAY,CAAC;CAC9D;AACD,mCAAmC;AACnC,MAAM,MAAM,YAAY,GAAG,YAAY,CAAC;AACxC,uCAAuC;AACvC,MAAM,MAAM,mBAAmB,GAAG,gBAAgB,CAAC;AAEnD;;;;;;;;;;GAUG;AACH,MAAM,MAAM,WAAW,GAAG,QAAQ,CAAC;IACjC,CAAC,EAAE,MAAM,CAAC;IACV,CAAC,EAAE,MAAM,CAAC;IACV,CAAC,EAAE,MAAM,CAAC;IACV,CAAC,EAAE,MAAM,CAAC;IACV,CAAC,EAAE,MAAM,CAAC;IACV,EAAE,EAAE,MAAM,CAAC;IACX,EAAE,EAAE,MAAM,CAAC;CACZ,CAAC,CAAC;AAEH;;;;;;GAMG;AACH,MAAM,MAAM,gBAAgB,GAAG,OAAO,CAAC;IACrC,EAAE,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;IACnB,EAAE,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;IACnB,MAAM,EAAE,OAAO,CAAC;IAChB,OAAO,EAAE,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,KAAK;QAAE,OAAO,EAAE,OAAO,CAAC;QAAC,KAAK,EAAE,MAAM,CAAA;KAAE,CAAC;CACxE,CAAC,CAAC;AAEH;;;;;;;;;GASG;AACH,MAAM,MAAM,SAAS,GAAG,OAAO,CAAC;IAC9B,iBAAiB,EAAE,CAAC,KAAK,EAAE,UAAU,KAAK,UAAU,CAAC;IACrD,MAAM,EAAE,CAAC,IAAI,EAAE,UAAU,EAAE,GAAG,EAAE,UAAU,EAAE,MAAM,EAAE,OAAO,KAAK,UAAU,CAAC;IAC3E,UAAU,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,WAAW,CAAC,MAAM,CAAC,CAAC;IACtD,OAAO,EAAE,KAAK,CAAC;IACf,WAAW,EAAE,CAAC,WAAW,CAAC,EAAE,MAAM,KAAK,UAAU,CAAC;CACnD,CAAC,CAAC;AAEH;;;;GAIG;AACH,MAAM,WAAW,KAAK;IACpB,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE,UAAU,KAAK;QAAE,SAAS,EAAE,UAAU,CAAC;QAAC,SAAS,EAAE,UAAU,CAAA;KAAE,CAAC;IAChF,YAAY,EAAE,CAAC,SAAS,EAAE,GAAG,KAAK,UAAU,CAAC;IAC7C,IAAI,EAAE,CAAC,OAAO,EAAE,GAAG,EAAE,SAAS,EAAE,GAAG,EAAE,OAAO,CAAC,EAAE;QAAE,OAAO,CAAC,EAAE,GAAG,CAAA;KAAE,KAAK,UAAU,CAAC;IAChF,MAAM,EAAE,CACN,GAAG,EAAE,GAAG,EACR,OAAO,EAAE,GAAG,EACZ,SAAS,EAAE,GAAG,EACd,OAAO,CAAC,EAAE;QAAE,OAAO,CAAC,EAAE,GAAG,CAAC;QAAC,MAAM,EAAE,OAAO,CAAA;KAAE,KACzC,OAAO,CAAC;IACb,KAAK,EAAE,gBAAgB,CAAC;IACxB,KAAK,EAAE;QACL,eAAe,EAAE,CAAC,IAAI,CAAC,EAAE,UAAU,KAAK,UAAU,CAAC;QACnD,gBAAgB,EAAE,CAAC,SAAS,EAAE,UAAU,KAAK,OAAO,CAAC;QACrD,gBAAgB,EAAE,CAAC,SAAS,EAAE,UAAU,EAAE,MAAM,CAAC,EAAE,OAAO,KAAK,OAAO,CAAC;QAEvE;;;;;;;;;;;;;;WAcG;QACH,YAAY,EAAE,CAAC,SAAS,EAAE,UAAU,KAAK,UAAU,CAAC;QACpD;;;;;;;;WAQG;QACH,gBAAgB,EAAE,CAAC,UAAU,EAAE,UAAU,KAAK,UAAU,CAAC;QACzD,oBAAoB,EAAE,CAAC,GAAG,EAAE,GAAG,KAAK;YAClC,IAAI,EAAE,UAAU,CAAC;YACjB,MAAM,EAAE,UAAU,CAAC;YACnB,MAAM,EAAE,MAAM,CAAC;YACf,KAAK,EAAE,YAAY,CAAC;YACpB,UAAU,EAAE,UAAU,CAAC;SACxB,CAAC;QAEF,wCAAwC;QACxC,gBAAgB,EAAE,CAAC,IAAI,CAAC,EAAE,UAAU,KAAK,UAAU,CAAC;QACpD,2CAA2C;QAC3C,UAAU,EAAE,CAAC,UAAU,CAAC,EAAE,MAAM,EAAE,KAAK,CAAC,EAAE,YAAY,KAAK,YAAY,CAAC;KACzE,CAAC;IACF,OAAO,EAAE,YAAY,CAAC;CACvB;AAUD,wBAAgB,OAAO,CAAC,MAAM,EAAE,WAAW,EAAE,SAAS,GAAE,gBAAqB,GAAG,gBAAgB,CA2U/F;AAED;;;;GAIG;AACH,8BAAsB,iBAAiB,CAAC,CAAC,SAAS,iBAAiB,CAAC,CAAC,CAAC,CACpE,YAAW,UAAU,CAAC,MAAM,EAAE,CAAC,CAAC;IAEhC,MAAM,CAAC,IAAI,EAAE,iBAAiB,CAAC,GAAG,CAAC,CAAC;IACpC,MAAM,CAAC,IAAI,EAAE,iBAAiB,CAAC,GAAG,CAAC,CAAC;IACpC,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;IAC1B,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;IAE1B,SAAS,CAAC,QAAQ,CAAC,EAAE,EAAE,YAAY,CAAC;gBAExB,EAAE,EAAE,YAAY;IAK5B,QAAQ,CAAC,OAAO,IAAI,UAAU;IAC9B,QAAQ,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,OAAO;IAGlC,MAAM,CAAC,SAAS,CAAC,MAAM,EAAE,UAAU,GAAG,GAAG;IAIzC,MAAM,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,GAAG,GAAG;IAI9B,IAAI,CAAC,IAAI,MAAM,CAEd;IACD,IAAI,CAAC,IAAI,MAAM,CAEd;IAGD,aAAa,IAAI,CAAC;IAKlB,cAAc,IAAI,IAAI;IAItB,QAAQ,CAAC,SAAS,CAAC,EAAE,MAAM,GAAG,WAAW,CAAC,MAAM,CAAC;IAIjD,KAAK,IAAI,MAAM;IAIf,QAAQ,IAAI,MAAM;IAIlB,aAAa,IAAI,OAAO;IAIxB,YAAY,IAAI,OAAO;IAIvB,GAAG,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC;IAKhB,QAAQ,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC;IAKrB,QAAQ,CAAC,MAAM,EAAE,MAAM,GAAG,CAAC;IAI3B,cAAc,CAAC,MAAM,EAAE,MAAM,GAAG,CAAC;IAIjC,MAAM,IAAI,CAAC;IAIX,MAAM,IAAI,CAAC;IAIX,UAAU,CAAC,UAAU,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE,OAAO,GAAG,CAAC;IAKpD,QAAQ,CAAC,GAAG,IAAI,OAAO;IACvB,SAAS,CAAC,QAAQ,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC,GAAG,IAAI;IAC7C,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,EAAE,YAAY,GAAG,CAAC;IAE5C,gCAAgC;IAChC,UAAU,IAAI,UAAU;CAGzB;AAED;;GAEG;AACH,wBAAgB,KAAK,CAAC,KAAK,EAAE,gBAAgB,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS,GAAE,SAAc,GAAG,KAAK,CA6L7F;AAGD,MAAM,MAAM,SAAS,GAAG,UAAU,CAAC,MAAM,CAAC,GAAG;IAC3C,CAAC,EAAE,MAAM,CAAC;IACV,CAAC,EAAE,MAAM,CAAC;IACV,+DAA+D;IAC/D,IAAI,EAAE,KAAK,CAAC;IACZ,WAAW,CAAC,EAAE,CAAC,WAAW,CAAC,EAAE,MAAM,KAAK,UAAU,CAAC;IACnD,iBAAiB,CAAC,EAAE,CAAC,KAAK,EAAE,UAAU,KAAK,UAAU,CAAC;IACtD,MAAM,CAAC,EAAE,CAAC,IAAI,EAAE,UAAU,EAAE,GAAG,EAAE,UAAU,EAAE,MAAM,EAAE,OAAO,KAAK,UAAU,CAAC;IAC5E,OAAO,CAAC,EAAE,OAAO,CAAC;IAClB,OAAO,CAAC,EAAE,KAAK,CAAC;IAChB,UAAU,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,WAAW,CAAC,MAAM,CAAC,CAAC;CACxD,CAAC;AACF,MAAM,MAAM,mBAAmB,GAAG,QAAQ,CAAC,SAAS,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC;AACzE,MAAM,MAAM,OAAO,GAAG;IACpB,+DAA+D;IAC/D,KAAK,EAAE,SAAS,CAAC;IACjB,MAAM,EAAE,KAAK,CAAC,QAAQ,CAAC,CAAC;IACxB,YAAY,EAAE,KAAK,CAAC,cAAc,CAAC,CAAC;IACpC,IAAI,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;IACpB,MAAM,EAAE,KAAK,CAAC,QAAQ,CAAC,CAAC;IACxB,KAAK,EAAE,gBAAgB,CAAC;IACxB,8BAA8B;IAC9B,aAAa,EAAE,gBAAgB,CAAC;IAChC,KAAK,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;IACtB,OAAO,EAAE,YAAY,CAAC;CACvB,CAAC;AACF,MAAM,MAAM,UAAU,GAAG;IACvB,KAAK,EAAE,WAAW,CAAC;IACnB,SAAS,EAAE,gBAAgB,CAAC;IAC5B,IAAI,EAAE,KAAK,CAAC;IACZ,SAAS,EAAE,SAAS,CAAC;CACtB,CAAC;AAkCF,wBAAgB,cAAc,CAAC,CAAC,EAAE,mBAAmB,GAAG,OAAO,CAK9D"}