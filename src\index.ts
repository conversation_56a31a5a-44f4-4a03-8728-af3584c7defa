import express from 'express';
import cors from 'cors';
import { createServer } from 'http';
import { WebSocketServer } from 'ws';
import dotenv from 'dotenv';
import { errorHandler } from './middleware/errorHandler.js';
import healthRoutes from './routes/health.js';
import solanaRoutes from './routes/solana.js';
import { websocketService } from './services/websocketService.js';

// Load environment variables
dotenv.config();

const app = express();
const server = createServer(app);
const wss = new WebSocketServer({ server });

// Middleware
app.use(cors());
app.use(express.json());

// Routes
app.use('/api/health', healthRoutes);
app.use('/api/solana', solanaRoutes);

// Add WebSocket stats endpoint
app.get('/api/stats', (req, res) => {
  res.json({
    success: true,
    data: websocketService.getStats()
  });
});

// WebSocket handling for real-time streaming
wss.on('connection', (ws) => {
  websocketService.handleConnection(ws);
});

// Error handling middleware
app.use(errorHandler);

const PORT = process.env.PORT || 3000;

server.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
  console.log(`WebSocket server ready for connections`);
});

export { wss };