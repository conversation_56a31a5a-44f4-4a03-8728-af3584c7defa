import { Request, Response } from 'express';
import { Connection, PublicKey, clusterApiUrl } from '@solana/web3.js';
import WebSocket from 'ws';

// Initialize Solana connection
// Using devnet for testing as it's more reliable for WebSocket subscriptions
const SOLANA_RPC_URL = process.env.SOLANA_RPC_URL || clusterApiUrl('devnet');

// Use HTTP endpoint for regular RPC calls
const connection = new Connection(SOLANA_RPC_URL, 'confirmed');

// For WebSocket subscriptions, we'll use the same connection
// The @solana/web3.js library will automatically handle WebSocket connections for subscriptions
console.log(`Connecting to Solana RPC: ${SOLANA_RPC_URL}`);

// Store active subscriptions
const activeSubscriptions = new Map<string, number>();

// REST API Controllers
export const getAccountInfo = async (req: Request, res: Response): Promise<void> => {
  try {
    const { address } = req.params;
    const publicKey = new PublicKey(address);
    
    const accountInfo = await connection.getAccountInfo(publicKey);
    
    res.json({
      success: true,
      data: {
        address,
        accountInfo: accountInfo ? {
          lamports: accountInfo.lamports,
          owner: accountInfo.owner.toString(),
          executable: accountInfo.executable,
          rentEpoch: accountInfo.rentEpoch,
          data: accountInfo.data.toString('base64')
        } : null
      }
    });
  } catch (error) {
    console.error('Error fetching account info:', error);
    res.status(400).json({
      success: false,
      error: 'Invalid account address or RPC error'
    });
  }
};

export const getSlot = async (req: Request, res: Response): Promise<void> => {
  try {
    const slot = await connection.getSlot();
    
    res.json({
      success: true,
      data: { slot }
    });
  } catch (error) {
    console.error('Error fetching slot:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch current slot'
    });
  }
};

export const getBlockHeight = async (req: Request, res: Response): Promise<void> => {
  try {
    const blockHeight = await connection.getBlockHeight();
    
    res.json({
      success: true,
      data: { blockHeight }
    });
  } catch (error) {
    console.error('Error fetching block height:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch block height'
    });
  }
};

// WebSocket Subscription Controllers
export const subscribeToAccount = (req: Request, res: Response): void => {
  const { address, encoding = 'base64' } = req.body;
  
  if (!address) {
    res.status(400).json({
      success: false,
      error: 'Account address is required'
    });
    return;
  }

  try {
    const publicKey = new PublicKey(address);
    const subscriptionId = `account_${address}_${Date.now()}`;
    
    res.json({
      success: true,
      message: 'Account subscription initiated. Connect to WebSocket to receive updates.',
      subscriptionId,
      instructions: {
        websocket: 'Connect to WebSocket and send: {"type": "subscribe", "subscription": "' + subscriptionId + '"}'
      }
    });
  } catch (error) {
    res.status(400).json({
      success: false,
      error: 'Invalid account address'
    });
  }
};

export const subscribeToSlot = (req: Request, res: Response): void => {
  const subscriptionId = `slot_${Date.now()}`;
  
  res.json({
    success: true,
    message: 'Slot subscription initiated. Connect to WebSocket to receive updates.',
    subscriptionId,
    instructions: {
      websocket: 'Connect to WebSocket and send: {"type": "subscribe", "subscription": "' + subscriptionId + '"}'
    }
  });
};

export const subscribeToLogs = (req: Request, res: Response): void => {
  const { filter = 'all' } = req.body;
  const subscriptionId = `logs_${filter}_${Date.now()}`;
  
  res.json({
    success: true,
    message: 'Logs subscription initiated. Connect to WebSocket to receive updates.',
    subscriptionId,
    filter,
    instructions: {
      websocket: 'Connect to WebSocket and send: {"type": "subscribe", "subscription": "' + subscriptionId + '"}'
    }
  });
};

// WebSocket subscription handlers
export const handleAccountSubscription = async (ws: WebSocket, address: string): Promise<number | null> => {
  try {
    const publicKey = new PublicKey(address);

    // Add a delay to ensure connection is ready
    await new Promise(resolve => setTimeout(resolve, 100));

    const subscriptionId = connection.onAccountChange(
      publicKey,
      (accountInfo, context) => {
        const message = {
          type: 'account_update',
          data: {
            address,
            accountInfo: {
              lamports: accountInfo.lamports,
              owner: accountInfo.owner.toString(),
              executable: accountInfo.executable,
              rentEpoch: accountInfo.rentEpoch,
              data: accountInfo.data.toString('base64')
            },
            context: {
              slot: context.slot
            }
          },
          timestamp: new Date().toISOString()
        };

        if (ws.readyState === WebSocket.OPEN) {
          ws.send(JSON.stringify(message));
        }
      },
      'confirmed'
    );

    console.log(`Account subscription created with ID: ${subscriptionId}`);
    return subscriptionId;
  } catch (error) {
    console.error('Error setting up account subscription:', error);

    // Send error message to client
    if (ws.readyState === WebSocket.OPEN) {
      ws.send(JSON.stringify({
        type: 'error',
        message: `Failed to subscribe to account ${address}: ${error.message}`,
        timestamp: new Date().toISOString()
      }));
    }

    return null;
  }
};

export const handleSlotSubscription = async (ws: WebSocket): Promise<number | null> => {
  try {
    // Add a delay to ensure connection is ready
    await new Promise(resolve => setTimeout(resolve, 100));

    const subscriptionId = connection.onSlotChange((slotInfo) => {
      const message = {
        type: 'slot_update',
        data: {
          slot: slotInfo.slot,
          parent: slotInfo.parent,
          root: slotInfo.root
        },
        timestamp: new Date().toISOString()
      };

      if (ws.readyState === WebSocket.OPEN) {
        ws.send(JSON.stringify(message));
      }
    });

    console.log(`Slot subscription created with ID: ${subscriptionId}`);
    return subscriptionId;
  } catch (error) {
    console.error('Error setting up slot subscription:', error);

    // Send error message to client
    if (ws.readyState === WebSocket.OPEN) {
      ws.send(JSON.stringify({
        type: 'error',
        message: `Failed to subscribe to slot updates: ${error.message}`,
        timestamp: new Date().toISOString()
      }));
    }

    return null;
  }
};

export const handleLogsSubscription = async (ws: WebSocket, filter: string = 'all'): Promise<number | null> => {
  try {
    // Add a delay to ensure connection is ready
    await new Promise(resolve => setTimeout(resolve, 100));

    const subscriptionId = connection.onLogs(
      filter === 'all' ? 'all' : { mentions: [filter] },
      (logs, context) => {
        const message = {
          type: 'logs_update',
          data: {
            logs: logs.logs,
            signature: logs.signature,
            err: logs.err,
            context: {
              slot: context.slot
            }
          },
          timestamp: new Date().toISOString()
        };

        if (ws.readyState === WebSocket.OPEN) {
          ws.send(JSON.stringify(message));
        }
      },
      'confirmed'
    );

    console.log(`Logs subscription created with ID: ${subscriptionId}`);
    return subscriptionId;
  } catch (error) {
    console.error('Error setting up logs subscription:', error);

    // Send error message to client
    if (ws.readyState === WebSocket.OPEN) {
      ws.send(JSON.stringify({
        type: 'error',
        message: `Failed to subscribe to logs: ${error.message}`,
        timestamp: new Date().toISOString()
      }));
    }

    return null;
  }
};

// Cleanup function for subscriptions
export const removeSubscription = async (subscriptionId: number): Promise<void> => {
  try {
    await connection.removeAccountChangeListener(subscriptionId);
    console.log(`Removed subscription with ID: ${subscriptionId}`);
  } catch (error) {
    console.error('Error removing subscription:', error);
  }
};

export { connection, activeSubscriptions };
