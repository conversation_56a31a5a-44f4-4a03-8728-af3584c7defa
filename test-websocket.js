// Simple WebSocket test script
import WebSocket from 'ws';

const ws = new WebSocket('ws://localhost:3000');

ws.on('open', function open() {
  console.log('✅ Connected to WebSocket server');
  
  // Test slot subscription
  console.log('📡 Subscribing to slot updates...');
  ws.send(JSON.stringify({
    type: 'subscribe',
    subscription: 'slot'
  }));
  
  // Test account subscription after a delay
  setTimeout(() => {
    console.log('📡 Subscribing to account updates...');
    ws.send(JSON.stringify({
      type: 'subscribe',
      subscription: 'account_11111111111111111111111111111112',
      params: { address: '11111111111111111111111111111112' }
    }));
  }, 2000);
});

ws.on('message', function message(data) {
  const parsed = JSON.parse(data.toString());
  console.log('📨 Received:', JSON.stringify(parsed, null, 2));
});

ws.on('close', function close() {
  console.log('❌ Disconnected from WebSocket server');
});

ws.on('error', function error(err) {
  console.error('🚨 WebSocket error:', err);
});

// Keep the script running for 30 seconds
setTimeout(() => {
  console.log('⏰ Test completed, closing connection...');
  ws.close();
  process.exit(0);
}, 9000);
