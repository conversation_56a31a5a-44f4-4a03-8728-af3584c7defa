{"version": 3, "file": "curve.d.ts", "sourceRoot": "", "sources": ["../../src/abstract/curve.ts"], "names": [], "mappings": "AAOA,OAAO,EAAgD,KAAK,MAAM,EAAE,MAAM,cAAc,CAAC;AAKzF,MAAM,MAAM,WAAW,CAAC,CAAC,IAAI;IAC3B,CAAC,EAAE,CAAC,CAAC;IACL,CAAC,EAAE,CAAC,CAAC;CACN,GAAG;IAAE,CAAC,CAAC,EAAE,KAAK,CAAA;CAAE,CAAC;AAIlB,MAAM,WAAW,KAAK,CAAC,CAAC,SAAS,KAAK,CAAC,CAAC,CAAC;IACvC,MAAM,IAAI,CAAC,CAAC;IACZ,MAAM,IAAI,CAAC,CAAC;IACZ,GAAG,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;IACjB,QAAQ,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;IACtB,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,OAAO,CAAC;IAC1B,QAAQ,CAAC,MAAM,EAAE,MAAM,GAAG,CAAC,CAAC;IAC5B,QAAQ,CAAC,CAAC,SAAS,CAAC,EAAE,GAAG,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC;CAC9C;AAUD,oDAAoD;AACpD,MAAM,WAAW,UAAU,CAAC,CAAC,EAAE,CAAC,SAAS,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC,CAAE,SAAQ,KAAK,CAAC,CAAC,CAAC;IACzE,8EAA8E;IAC9E,CAAC,EAAE,CAAC,CAAC;IACL,8EAA8E;IAC9E,CAAC,EAAE,CAAC,CAAC;IACL,CAAC,CAAC,EAAE,CAAC,CAAC;IACN,MAAM,IAAI,CAAC,CAAC;IACZ,MAAM,IAAI,CAAC,CAAC;IACZ,GAAG,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;IACjB,QAAQ,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;IACtB,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,OAAO,CAAC;IAC1B,QAAQ,CAAC,MAAM,EAAE,MAAM,GAAG,CAAC,CAAC;IAC5B,cAAc,IAAI,IAAI,CAAC;IACvB,aAAa,IAAI,CAAC,CAAC;IACnB,GAAG,IAAI,OAAO,CAAC;IACf,aAAa,IAAI,OAAO,CAAC;IACzB,YAAY,IAAI,OAAO,CAAC;IACxB,cAAc,CAAC,MAAM,EAAE,MAAM,GAAG,CAAC,CAAC;IAClC;;;OAGG;IACH,UAAU,CAAC,UAAU,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE,OAAO,GAAG,CAAC,CAAC;IACrD,iDAAiD;IACjD,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;IACxC,OAAO,IAAI,UAAU,CAAC;IACtB,KAAK,IAAI,MAAM,CAAC;CACjB;AAED,gEAAgE;AAChE,MAAM,WAAW,cAAc,CAAC,CAAC,SAAS,UAAU,CAAC,GAAG,EAAE,CAAC,CAAC;IAC1D,CAAC,MAAM,CAAC,WAAW,CAAC,EAAE,CAAC,IAAI,EAAE,OAAO,KAAK,OAAO,CAAC;IACjD,IAAI,EAAE,CAAC,CAAC;IACR,IAAI,EAAE,CAAC,CAAC;IACR,iCAAiC;IACjC,EAAE,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,uDAAuD;IACvD,EAAE,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;IACnB,iGAAiG;IACjG,UAAU,CAAC,CAAC,EAAE,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACtC,SAAS,CAAC,KAAK,EAAE,UAAU,GAAG,CAAC,CAAC;IAChC,OAAO,CAAC,GAAG,EAAE,UAAU,GAAG,MAAM,GAAG,CAAC,CAAC;CACtC;AAaD,iDAAiD;AACjD,MAAM,MAAM,GAAG,CAAC,CAAC,SAAS,UAAU,CAAC,GAAG,EAAE,CAAC,CAAC,IAAI,CAAC,SAAS,UAAU,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;AAC7F,0DAA0D;AAC1D,MAAM,MAAM,IAAI,CAAC,EAAE,SAAS,cAAc,CAAC,UAAU,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC;AACrF,2DAA2D;AAC3D,MAAM,MAAM,IAAI,CAAC,EAAE,SAAS,cAAc,CAAC,UAAU,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,MAAM,CAAC,CAAC;AAgB/E,MAAM,MAAM,MAAM,GAAG,cAAc,CACjC,UAAU,CAAC,GAAG,EACd,UAAU,CAAC,GAAG,EACd,UAAU,CAAC,GAAG,EACd,UAAU,CAAC,GAAG,EACd,UAAU,CAAC,GAAG,EACd,UAAU,CAAC,GAAG,EACd,UAAU,CAAC,GAAG,EACd,UAAU,CAAC,GAAG,EACd,UAAU,CAAC,GAAG,EACd,UAAU,CAAC,GAAG,EAAE,GAAG,CAAC,CACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CACV,CAAC;AAEF,MAAM,WAAW,YAAY;IAC3B,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,qBAAqB,CAAC,EAAE,MAAM,CAAC;IAC/B,kBAAkB,CAAC,EAAE,OAAO,CAAC;IAC7B,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,IAAI,CAAC,EAAE,MAAM,CAAC;CACf;AACD,MAAM,MAAM,gBAAgB,CAAC,CAAC,IAAI;IAChC,IAAI,EAAE,CAAC,CAAC;IACR,IAAI,EAAE,CAAC,CAAC;CACT,CAAC;AACF,kBAAkB;AAClB,MAAM,MAAM,wBAAwB,CAAC,CAAC,IAAI,gBAAgB,CAAC,CAAC,CAAC,GAAG;IAC9D,EAAE,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC;IAChB,EAAE,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;IACnB,UAAU,CAAC,EAAE,EAAE,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;CACrC,CAAC;AACF,MAAM,MAAM,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,EAAE,CAAC;AAExC,wBAAgB,QAAQ,CAAC,CAAC,SAAS;IAAE,MAAM,EAAE,MAAM,CAAC,CAAA;CAAE,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,GAAG,CAAC,CAGtF;AAED;;;;;GAKG;AACH,wBAAgB,UAAU,CAAC,CAAC,SAAS,UAAU,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,EAAE,SAAS,cAAc,CAAC,CAAC,CAAC,EACnF,CAAC,EAAE,EAAE,EACL,MAAM,EAAE,CAAC,EAAE,GACV,CAAC,EAAE,CAML;AAOD,uDAAuD;AACvD,MAAM,MAAM,KAAK,GAAG;IAClB,OAAO,EAAE,MAAM,CAAC;IAChB,UAAU,EAAE,MAAM,CAAC;IACnB,IAAI,EAAE,MAAM,CAAC;IACb,SAAS,EAAE,MAAM,CAAC;IAClB,OAAO,EAAE,MAAM,CAAC;CACjB,CAAC;AAkEF;;;;;;;;;;;;;;;;;GAiBG;AACH,qBAAa,IAAI,CAAC,EAAE,SAAS,MAAM;IACjC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAW;IAChC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAW;IAChC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAW;IAC9B,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC;gBAGV,KAAK,EAAE,EAAE,EAAE,IAAI,EAAE,MAAM;IAQnC,aAAa,CAAC,GAAG,EAAE,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,GAAE,IAAI,CAAC,EAAE,CAAa,GAAG,IAAI,CAAC,EAAE,CAAC;IAU1E;;;;;;;;;;;OAWG;IACH,OAAO,CAAC,gBAAgB;IAkBxB;;;;;OAKG;IACH,OAAO,CAAC,IAAI;IAgCZ;;;;OAIG;IACH,OAAO,CAAC,UAAU;IAwBlB,OAAO,CAAC,cAAc;IActB,MAAM,CACJ,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,EACf,MAAM,EAAE,MAAM,EACd,SAAS,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,GAC3B;QAAE,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;QAAC,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,CAAA;KAAE;IAK/B,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,SAAS,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC;IAShG,WAAW,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,MAAM,GAAG,IAAI;IAMzC,QAAQ,CAAC,GAAG,EAAE,IAAI,CAAC,EAAE,CAAC,GAAG,OAAO;CAGjC;AAED;;;GAGG;AACH,wBAAgB,aAAa,CAAC,CAAC,SAAS,UAAU,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,EAAE,SAAS,cAAc,CAAC,CAAC,CAAC,EACtF,KAAK,EAAE,EAAE,EACT,KAAK,EAAE,CAAC,EACR,EAAE,EAAE,MAAM,EACV,EAAE,EAAE,MAAM,GACT;IAAE,EAAE,EAAE,CAAC,CAAC;IAAC,EAAE,EAAE,CAAC,CAAA;CAAE,CAYlB;AAED;;;;;;;;;GASG;AACH,wBAAgB,SAAS,CAAC,CAAC,SAAS,UAAU,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,EAAE,SAAS,cAAc,CAAC,CAAC,CAAC,EAClF,CAAC,EAAE,EAAE,EACL,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,EACtB,MAAM,EAAE,CAAC,EAAE,EACX,OAAO,EAAE,MAAM,EAAE,GAChB,CAAC,CAwCH;AACD;;;;;;GAMG;AACH,wBAAgB,mBAAmB,CAAC,CAAC,SAAS,UAAU,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,EAAE,SAAS,cAAc,CAAC,CAAC,CAAC,EAC5F,CAAC,EAAE,EAAE,EACL,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,EACtB,MAAM,EAAE,CAAC,EAAE,EACX,UAAU,EAAE,MAAM,GACjB,CAAC,OAAO,EAAE,MAAM,EAAE,KAAK,CAAC,CAoE1B;AAGD;;;GAGG;AACH,MAAM,MAAM,UAAU,CAAC,CAAC,IAAI;IAC1B,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,EAAE,MAAM,CAAC;IACV,UAAU,CAAC,EAAE,MAAM,CAAC;IACpB,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,CAAC,EAAE,MAAM,CAAC;IACV,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,EAAE,EAAE,CAAC,CAAC;IACN,EAAE,EAAE,CAAC,CAAC;IACN,kBAAkB,CAAC,EAAE,OAAO,CAAC;CAC9B,CAAC;AAGF,kBAAkB;AAClB,wBAAgB,aAAa,CAAC,EAAE,EAAE,CAAC,EACjC,KAAK,EAAE,UAAU,CAAC,EAAE,CAAC,GAAG,CAAC,GACxB,QAAQ,CACT;IACE,QAAQ,CAAC,UAAU,EAAE,MAAM,CAAC;IAC5B,QAAQ,CAAC,WAAW,EAAE,MAAM,CAAC;CAC9B,GAAG,UAAU,CAAC,EAAE,CAAC,GAChB,CAAC,GAAG;IACF,CAAC,EAAE,MAAM,CAAC;CACX,CACJ,CAqBA;AAED,MAAM,MAAM,gBAAgB,CAAC,CAAC,IAAI;IAChC,CAAC,EAAE,MAAM,CAAC;IACV,CAAC,EAAE,MAAM,CAAC;IACV,CAAC,EAAE,MAAM,CAAC;IACV,CAAC,EAAE,CAAC,CAAC;IACL,CAAC,CAAC,EAAE,CAAC,CAAC;IACN,CAAC,CAAC,EAAE,CAAC,CAAC;IACN,EAAE,EAAE,CAAC,CAAC;IACN,EAAE,EAAE,CAAC,CAAC;CACP,CAAC;AAWF,MAAM,MAAM,IAAI,CAAC,CAAC,IAAI;IAAE,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC;IAAC,EAAE,EAAE,MAAM,CAAC,MAAM,CAAC,CAAA;CAAE,CAAC;AAE5D,8CAA8C;AAC9C,wBAAgB,kBAAkB,CAAC,CAAC,EAClC,IAAI,EAAE,aAAa,GAAG,SAAS,EAC/B,KAAK,EAAE,gBAAgB,CAAC,CAAC,CAAC,EAC1B,SAAS,GAAE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAM,EAChC,MAAM,CAAC,EAAE,OAAO,GACf,IAAI,CAAC,CAAC,CAAC,GAAG;IAAE,KAAK,EAAE,gBAAgB,CAAC,CAAC,CAAC,CAAA;CAAE,CAmB1C"}