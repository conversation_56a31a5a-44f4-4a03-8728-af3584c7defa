{"version": 3, "file": "curve.js", "sourceRoot": "", "sources": ["../src/abstract/curve.ts"], "names": [], "mappings": ";;;AAoJA,4BAGC;AAQD,gCASC;AA2QD,sCAiBC;AAYD,8BA6CC;AAQD,kDAyEC;AAqBD,sCA+BC;AAyBD,gDAwBC;AAnrBD;;;;GAIG;AACH,sEAAsE;AACtE,0CAA8D;AAC9D,6CAAyF;AAEzF,MAAM,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AACtB,MAAM,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AA0ItB,SAAgB,QAAQ,CAAgC,SAAkB,EAAE,IAAO;IACjF,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;IAC1B,OAAO,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC;AAChC,CAAC;AAED;;;;;GAKG;AACH,SAAgB,UAAU,CACxB,CAAK,EACL,MAAW;IAEX,MAAM,UAAU,GAAG,IAAA,0BAAa,EAC9B,CAAC,CAAC,EAAE,EACJ,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAE,CAAC,CACxB,CAAC;IACF,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACvE,CAAC;AAED,SAAS,SAAS,CAAC,CAAS,EAAE,IAAY;IACxC,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI;QAChD,MAAM,IAAI,KAAK,CAAC,oCAAoC,GAAG,IAAI,GAAG,WAAW,GAAG,CAAC,CAAC,CAAC;AACnF,CAAC;AAWD,SAAS,SAAS,CAAC,CAAS,EAAE,UAAkB;IAC9C,SAAS,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;IACzB,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,uCAAuC;IACtF,MAAM,UAAU,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,yCAAyC;IAC1E,MAAM,SAAS,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,UAAU;IACpC,MAAM,IAAI,GAAG,IAAA,kBAAO,EAAC,CAAC,CAAC,CAAC,CAAC,6BAA6B;IACtD,MAAM,OAAO,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ;IACnC,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,CAAC;AAC3D,CAAC;AAED,SAAS,WAAW,CAAC,CAAS,EAAE,MAAc,EAAE,KAAY;IAC1D,MAAM,EAAE,UAAU,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,KAAK,CAAC;IACvD,IAAI,KAAK,GAAG,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,kBAAkB;IAChD,IAAI,KAAK,GAAG,CAAC,IAAI,OAAO,CAAC,CAAC,0BAA0B;IAEpD,8BAA8B;IAC9B,kDAAkD;IAClD,uCAAuC;IACvC,6DAA6D;IAE7D,sCAAsC;IACtC,IAAI,KAAK,GAAG,UAAU,EAAE,CAAC;QACvB,mEAAmE;QACnE,KAAK,IAAI,SAAS,CAAC,CAAC,qEAAqE;QACzF,KAAK,IAAI,GAAG,CAAC,CAAC,eAAe;IAC/B,CAAC;IACD,MAAM,WAAW,GAAG,MAAM,GAAG,UAAU,CAAC;IACxC,MAAM,MAAM,GAAG,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,0BAA0B;IAC5E,MAAM,MAAM,GAAG,KAAK,KAAK,CAAC,CAAC,CAAC,+BAA+B;IAC3D,MAAM,KAAK,GAAG,KAAK,GAAG,CAAC,CAAC,CAAC,oCAAoC;IAC7D,MAAM,MAAM,GAAG,MAAM,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,kCAAkC;IACnE,MAAM,OAAO,GAAG,WAAW,CAAC,CAAC,wBAAwB;IACrD,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC;AAC3D,CAAC;AAED,SAAS,iBAAiB,CAAC,MAAa,EAAE,CAAM;IAC9C,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC;QAAE,MAAM,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAC;IAC9D,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;QACtB,IAAI,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC;YAAE,MAAM,IAAI,KAAK,CAAC,yBAAyB,GAAG,CAAC,CAAC,CAAC;IACxE,CAAC,CAAC,CAAC;AACL,CAAC;AACD,SAAS,kBAAkB,CAAC,OAAc,EAAE,KAAU;IACpD,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC;QAAE,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;IAC1E,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;QACvB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;YAAE,MAAM,IAAI,KAAK,CAAC,0BAA0B,GAAG,CAAC,CAAC,CAAC;IACzE,CAAC,CAAC,CAAC;AACL,CAAC;AAED,mFAAmF;AACnF,iDAAiD;AACjD,4CAA4C;AAC5C,MAAM,gBAAgB,GAAG,IAAI,OAAO,EAAc,CAAC;AACnD,MAAM,gBAAgB,GAAG,IAAI,OAAO,EAAe,CAAC;AAEpD,SAAS,IAAI,CAAC,CAAM;IAClB,0BAA0B;IAC1B,YAAY;IACZ,OAAO,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;AACtC,CAAC;AAED,SAAS,OAAO,CAAC,CAAS;IACxB,IAAI,CAAC,KAAK,GAAG;QAAE,MAAM,IAAI,KAAK,CAAC,cAAc,CAAC,CAAC;AACjD,CAAC;AAED;;;;;;;;;;;;;;;;;GAiBG;AACH,MAAa,IAAI;IAMf,+DAA+D;IAC/D,YAAY,KAAS,EAAE,IAAY;QACjC,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;QACvB,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;QACvB,IAAI,CAAC,EAAE,GAAG,KAAK,CAAC,EAAE,CAAC;QACnB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IACnB,CAAC;IAED,uCAAuC;IACvC,aAAa,CAAC,GAAa,EAAE,CAAS,EAAE,IAAc,IAAI,CAAC,IAAI;QAC7D,IAAI,CAAC,GAAa,GAAG,CAAC;QACtB,OAAO,CAAC,GAAG,GAAG,EAAE,CAAC;YACf,IAAI,CAAC,GAAG,GAAG;gBAAE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAC1B,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC;YACf,CAAC,KAAK,GAAG,CAAC;QACZ,CAAC;QACD,OAAO,CAAC,CAAC;IACX,CAAC;IAED;;;;;;;;;;;OAWG;IACK,gBAAgB,CAAC,KAAe,EAAE,CAAS;QACjD,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,GAAG,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;QACxD,MAAM,MAAM,GAAe,EAAE,CAAC;QAC9B,IAAI,CAAC,GAAa,KAAK,CAAC;QACxB,IAAI,IAAI,GAAG,CAAC,CAAC;QACb,KAAK,IAAI,MAAM,GAAG,CAAC,EAAE,MAAM,GAAG,OAAO,EAAE,MAAM,EAAE,EAAE,CAAC;YAChD,IAAI,GAAG,CAAC,CAAC;YACT,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClB,oBAAoB;YACpB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC,EAAE,EAAE,CAAC;gBACpC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;gBACnB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACpB,CAAC;YACD,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;QACpB,CAAC;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACK,IAAI,CAAC,CAAS,EAAE,WAAuB,EAAE,CAAS;QACxD,4CAA4C;QAC5C,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC;YAAE,MAAM,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAC;QAC3D,eAAe;QACf,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC;QAClB,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC;QAClB,6FAA6F;QAC7F,qFAAqF;QACrF,0EAA0E;QAC1E,+EAA+E;QAC/E,2EAA2E;QAC3E,MAAM,EAAE,GAAG,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;QACnC,KAAK,IAAI,MAAM,GAAG,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE,CAAC;YACnD,qFAAqF;YACrF,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,WAAW,CAAC,CAAC,EAAE,MAAM,EAAE,EAAE,CAAC,CAAC;YACrF,CAAC,GAAG,KAAK,CAAC;YACV,IAAI,MAAM,EAAE,CAAC;gBACX,wCAAwC;gBACxC,6EAA6E;gBAC7E,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,MAAM,EAAE,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YACpD,CAAC;iBAAM,CAAC;gBACN,kCAAkC;gBAClC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,EAAE,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YAClD,CAAC;QACH,CAAC;QACD,OAAO,CAAC,CAAC,CAAC,CAAC;QACX,2DAA2D;QAC3D,wEAAwE;QACxE,4DAA4D;QAC5D,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IAClB,CAAC;IAED;;;;OAIG;IACK,UAAU,CAChB,CAAS,EACT,WAAuB,EACvB,CAAS,EACT,MAAgB,IAAI,CAAC,IAAI;QAEzB,MAAM,EAAE,GAAG,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;QACnC,KAAK,IAAI,MAAM,GAAG,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE,CAAC;YACnD,IAAI,CAAC,KAAK,GAAG;gBAAE,MAAM,CAAC,2BAA2B;YACjD,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,WAAW,CAAC,CAAC,EAAE,MAAM,EAAE,EAAE,CAAC,CAAC;YACpE,CAAC,GAAG,KAAK,CAAC;YACV,IAAI,MAAM,EAAE,CAAC;gBACX,sCAAsC;gBACtC,uBAAuB;gBACvB,SAAS;YACX,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC;gBACjC,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,0CAA0C;YACzF,CAAC;QACH,CAAC;QACD,OAAO,CAAC,CAAC,CAAC,CAAC;QACX,OAAO,GAAG,CAAC;IACb,CAAC;IAEO,cAAc,CAAC,CAAS,EAAE,KAAe,EAAE,SAA4B;QAC7E,yDAAyD;QACzD,IAAI,IAAI,GAAG,gBAAgB,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QACvC,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,IAAI,GAAG,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC,CAAe,CAAC;YACrD,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;gBACZ,oDAAoD;gBACpD,IAAI,OAAO,SAAS,KAAK,UAAU;oBAAE,IAAI,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC;gBAC5D,gBAAgB,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;YACpC,CAAC;QACH,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,MAAM,CACJ,KAAe,EACf,MAAc,EACd,SAA4B;QAE5B,MAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;QACtB,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC,EAAE,KAAK,EAAE,SAAS,CAAC,EAAE,MAAM,CAAC,CAAC;IACxE,CAAC;IAED,MAAM,CAAC,KAAe,EAAE,MAAc,EAAE,SAA4B,EAAE,IAAe;QACnF,MAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;QACtB,IAAI,CAAC,KAAK,CAAC;YAAE,OAAO,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC,+BAA+B;QAC5F,OAAO,IAAI,CAAC,UAAU,CAAC,CAAC,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC,EAAE,KAAK,EAAE,SAAS,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;IACpF,CAAC;IAED,mEAAmE;IACnE,wDAAwD;IACxD,2EAA2E;IAC3E,WAAW,CAAC,CAAW,EAAE,CAAS;QAChC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;QACxB,gBAAgB,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC3B,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IAC7B,CAAC;IAED,QAAQ,CAAC,GAAa;QACpB,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;IACzB,CAAC;CACF;AAnKD,oBAmKC;AAED;;;GAGG;AACH,SAAgB,aAAa,CAC3B,KAAS,EACT,KAAQ,EACR,EAAU,EACV,EAAU;IAEV,IAAI,GAAG,GAAG,KAAK,CAAC;IAChB,IAAI,EAAE,GAAG,KAAK,CAAC,IAAI,CAAC;IACpB,IAAI,EAAE,GAAG,KAAK,CAAC,IAAI,CAAC;IACpB,OAAO,EAAE,GAAG,GAAG,IAAI,EAAE,GAAG,GAAG,EAAE,CAAC;QAC5B,IAAI,EAAE,GAAG,GAAG;YAAE,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAC/B,IAAI,EAAE,GAAG,GAAG;YAAE,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAC/B,GAAG,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC;QACnB,EAAE,KAAK,GAAG,CAAC;QACX,EAAE,KAAK,GAAG,CAAC;IACb,CAAC;IACD,OAAO,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;AACpB,CAAC;AAED;;;;;;;;;GASG;AACH,SAAgB,SAAS,CACvB,CAAK,EACL,MAAsB,EACtB,MAAW,EACX,OAAiB;IAEjB,+EAA+E;IAC/E,wEAAwE;IACxE,QAAQ;IACR,yCAAyC;IACzC,8DAA8D;IAC9D,2BAA2B;IAC3B,iBAAiB,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;IAC7B,kBAAkB,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;IACpC,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;IAC9B,MAAM,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC;IAC/B,IAAI,OAAO,KAAK,OAAO;QAAE,MAAM,IAAI,KAAK,CAAC,qDAAqD,CAAC,CAAC;IAChG,sEAAsE;IACtE,MAAM,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC;IACpB,MAAM,KAAK,GAAG,IAAA,iBAAM,EAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC;IACtC,IAAI,UAAU,GAAG,CAAC,CAAC,CAAC,OAAO;IAC3B,IAAI,KAAK,GAAG,EAAE;QAAE,UAAU,GAAG,KAAK,GAAG,CAAC,CAAC;SAClC,IAAI,KAAK,GAAG,CAAC;QAAE,UAAU,GAAG,KAAK,GAAG,CAAC,CAAC;SACtC,IAAI,KAAK,GAAG,CAAC;QAAE,UAAU,GAAG,CAAC,CAAC;IACnC,MAAM,IAAI,GAAG,IAAA,kBAAO,EAAC,UAAU,CAAC,CAAC;IACjC,MAAM,OAAO,GAAG,IAAI,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,oBAAoB;IAC5E,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,UAAU,CAAC,GAAG,UAAU,CAAC;IACzE,IAAI,GAAG,GAAG,IAAI,CAAC;IACf,KAAK,IAAI,CAAC,GAAG,QAAQ,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,UAAU,EAAE,CAAC;QAC/C,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACnB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,EAAE,CAAC,EAAE,EAAE,CAAC;YACjC,MAAM,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;YAC1B,MAAM,KAAK,GAAG,MAAM,CAAC,CAAC,MAAM,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;YACnD,OAAO,CAAC,KAAK,CAAC,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QACjD,CAAC;QACD,IAAI,IAAI,GAAG,IAAI,CAAC,CAAC,0DAA0D;QAC3E,wCAAwC;QACxC,KAAK,IAAI,CAAC,GAAG,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,IAAI,GAAG,IAAI,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;YACzD,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5B,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QACxB,CAAC;QACD,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QACpB,IAAI,CAAC,KAAK,CAAC;YAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC,EAAE;gBAAE,GAAG,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC;IACvE,CAAC;IACD,OAAO,GAAQ,CAAC;AAClB,CAAC;AACD;;;;;;GAMG;AACH,SAAgB,mBAAmB,CACjC,CAAK,EACL,MAAsB,EACtB,MAAW,EACX,UAAkB;IAElB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAkCG;IACH,SAAS,CAAC,UAAU,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC;IACnC,iBAAiB,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;IAC7B,MAAM,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC;IACpB,MAAM,SAAS,GAAG,CAAC,IAAI,UAAU,GAAG,CAAC,CAAC,CAAC,4BAA4B;IACnE,MAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,GAAG,UAAU,CAAC,CAAC,CAAC,iBAAiB;IACrE,MAAM,IAAI,GAAG,IAAA,kBAAO,EAAC,UAAU,CAAC,CAAC;IACjC,MAAM,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,CAAI,EAAE,EAAE;QACjC,MAAM,GAAG,GAAG,EAAE,CAAC;QACf,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,EAAE,EAAE,CAAC;YAC5C,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACd,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC;QACD,OAAO,GAAG,CAAC;IACb,CAAC,CAAC,CAAC;IACH,OAAO,CAAC,OAAiB,EAAK,EAAE;QAC9B,kBAAkB,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;QACpC,IAAI,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM;YAChC,MAAM,IAAI,KAAK,CAAC,uDAAuD,CAAC,CAAC;QAC3E,IAAI,GAAG,GAAG,IAAI,CAAC;QACf,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAChC,kDAAkD;YAClD,IAAI,GAAG,KAAK,IAAI;gBAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC,EAAE;oBAAE,GAAG,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC;YAC1E,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,GAAG,UAAU,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC;YACnE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;gBACxC,MAAM,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;gBACrB,MAAM,IAAI,GAAG,MAAM,CAAC,CAAC,CAAC,IAAI,OAAO,CAAC,GAAG,IAAI,CAAC,CAAC;gBAC3C,IAAI,CAAC,IAAI;oBAAE,SAAS,CAAC,2BAA2B;gBAChD,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;YACrC,CAAC;QACH,CAAC;QACD,OAAO,GAAG,CAAC;IACb,CAAC,CAAC;AACJ,CAAC;AAmBD,eAAe;AACf,kBAAkB;AAClB,SAAgB,aAAa,CAC3B,KAAyB;IAUzB,IAAA,0BAAa,EAAC,KAAK,CAAC,EAAE,CAAC,CAAC;IACxB,IAAA,yBAAc,EACZ,KAAK,EACL;QACE,CAAC,EAAE,QAAQ;QACX,CAAC,EAAE,QAAQ;QACX,EAAE,EAAE,OAAO;QACX,EAAE,EAAE,OAAO;KACZ,EACD;QACE,UAAU,EAAE,eAAe;QAC3B,WAAW,EAAE,eAAe;KAC7B,CACF,CAAC;IACF,eAAe;IACf,OAAO,MAAM,CAAC,MAAM,CAAC;QACnB,GAAG,IAAA,oBAAO,EAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,UAAU,CAAC;QACrC,GAAG,KAAK;QACR,GAAG,EAAE,CAAC,EAAE,KAAK,CAAC,EAAE,CAAC,KAAK,EAAE;KAChB,CAAC,CAAC;AACd,CAAC;AAaD,SAAS,WAAW,CAAI,KAAa,EAAE,KAAiB,EAAE,IAAc;IACtE,IAAI,KAAK,EAAE,CAAC;QACV,IAAI,KAAK,CAAC,KAAK,KAAK,KAAK;YAAE,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC;QAC7F,IAAA,0BAAa,EAAC,KAAK,CAAC,CAAC;QACrB,OAAO,KAAK,CAAC;IACf,CAAC;SAAM,CAAC;QACN,OAAO,IAAA,kBAAK,EAAC,KAAK,EAAE,EAAE,IAAI,EAAE,CAAyB,CAAC;IACxD,CAAC;AACH,CAAC;AAGD,8CAA8C;AAC9C,SAAgB,kBAAkB,CAChC,IAA+B,EAC/B,KAA0B,EAC1B,YAA8B,EAAE,EAChC,MAAgB;IAEhB,IAAI,MAAM,KAAK,SAAS;QAAE,MAAM,GAAG,IAAI,KAAK,SAAS,CAAC;IACtD,IAAI,CAAC,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ;QAAE,MAAM,IAAI,KAAK,CAAC,kBAAkB,IAAI,eAAe,CAAC,CAAC;IAChG,KAAK,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAU,EAAE,CAAC;QACzC,MAAM,GAAG,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QACrB,IAAI,CAAC,CAAC,OAAO,GAAG,KAAK,QAAQ,IAAI,GAAG,GAAG,GAAG,CAAC;YACzC,MAAM,IAAI,KAAK,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAC;IAC1D,CAAC;IACD,MAAM,EAAE,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,SAAS,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;IACtD,MAAM,EAAE,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,SAAS,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;IACtD,MAAM,EAAE,GAAc,IAAI,KAAK,aAAa,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;IACzD,MAAM,MAAM,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,EAAE,CAAU,CAAC;IAC9C,KAAK,MAAM,CAAC,IAAI,MAAM,EAAE,CAAC;QACvB,aAAa;QACb,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YACvB,MAAM,IAAI,KAAK,CAAC,SAAS,CAAC,0CAA0C,CAAC,CAAC;IAC1E,CAAC;IACD,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC,CAAC;IAChD,OAAO,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;AAC3B,CAAC"}